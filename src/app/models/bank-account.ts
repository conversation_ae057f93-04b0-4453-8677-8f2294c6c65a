export interface BankAccount {
  id: string;
  balance: number;
  createdAt: Date;
  status: AccountStatus;
  customerId: number;
  customerDTO?: {
    id: number;
    name: string;
    email: string;
  };
  type: string; // "CurrentAccount" or "SavingAccount"
}

export interface SavingBankAccount extends BankAccount {
  interestRate: number;
}

export interface CurrentBankAccount extends BankAccount {
  overDraft: number;
}

export enum AccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}
