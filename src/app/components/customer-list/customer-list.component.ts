import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Customer } from '../../models/customer';
import { CustomerService } from '../../services/customer.service';

@Component({
  selector: 'app-customer-list',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="customer-list-container">
      <!-- Header Section -->
      <div class="header-section">
        <div class="header-content">
          <h1 class="page-title">
            <span class="title-icon">👥</span>
            Customer Management
          </h1>
          <p class="page-subtitle">Manage your customers and their information</p>
        </div>
        <button class="add-customer-btn" (click)="onAddCustomer()" [disabled]="isLoading">
          <span class="btn-icon">+</span>
          <span class="btn-text">New Customer</span>
        </button>
      </div>

      <!-- Loading State -->
      <div class="loading-state" *ngIf="isLoading">
        <div class="loading-spinner"></div>
        <p>Loading customers...</p>
      </div>

      <!-- Error State -->
      <div class="error-state" *ngIf="errorMessage && !isLoading">
        <div class="error-icon">⚠️</div>
        <h3>Error Loading Customers</h3>
        <p>{{ errorMessage }}</p>
        <button class="retry-btn" (click)="retryLoad()">Retry</button>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview" *ngIf="!isLoading && !errorMessage && customers.length > 0">
        <div class="stat-card">
          <div class="stat-value">{{ customers.length }}</div>
          <div class="stat-label">Total Customers</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getActiveCustomersCount() }}</div>
          <div class="stat-label">Active Customers</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getRecentCustomersCount() }}</div>
          <div class="stat-label">Recent Additions</div>
        </div>
      </div>

      <!-- Customers Grid -->
      <div class="customers-grid" *ngIf="!isLoading && !errorMessage && customers.length > 0; else noCustomers">
        <div class="customer-card" *ngFor="let customer of customers; trackBy: trackByCustomerId">
          <!-- Card Header -->
          <div class="card-header">
            <div class="customer-avatar">
              <span class="avatar-icon">{{ getCustomerInitials(customer.name) }}</span>
            </div>
            <div class="customer-info">
              <h3 class="customer-name">{{ customer.name }}</h3>
              <p class="customer-email">{{ customer.email }}</p>
            </div>
          </div>

          <!-- Card Body -->
          <div class="card-body">
            <div class="customer-details">
              <div class="detail-item">
                <span class="detail-icon">🆔</span>
                <span class="detail-text">ID: {{ customer.id }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-icon">📧</span>
                <span class="detail-text">{{ customer.email }}</span>
              </div>
            </div>
          </div>

          <!-- Card Actions -->
          <div class="card-actions">
            <button class="action-btn primary" (click)="onViewAccounts(customer)" title="View Customer Accounts">
              <span class="btn-icon">🏦</span>
              <span class="btn-text">Accounts</span>
            </button>
            <button class="action-btn secondary" (click)="onEditCustomer(customer)" title="Edit Customer">
              <span class="btn-icon">✏️</span>
              <span class="btn-text">Edit</span>
            </button>
            <button class="action-btn danger" (click)="onDeleteCustomer(customer.id)" title="Delete Customer">
              <span class="btn-icon">🗑️</span>
              <span class="btn-text">Delete</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <ng-template #noCustomers>
        <div class="empty-state" *ngIf="!isLoading && !errorMessage">
          <div class="empty-icon">👥</div>
          <h2 class="empty-title">No Customers Found</h2>
          <p class="empty-description">Start by adding your first customer to the system.</p>
          <button class="empty-action-btn" (click)="onAddCustomer()">
            <span class="btn-icon">+</span>
            <span class="btn-text">Add First Customer</span>
          </button>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    .customer-list {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }

    .list-header h3 {
      margin: 0;
      color: #2c3e50;
    }

    .table-container {
      overflow-x: auto;
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #dee2e6;
    }

    .data-table th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }

    .data-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .actions {
      display: flex;
      gap: 0.5rem;
    }

    .no-data {
      padding: 3rem;
      text-align: center;
      color: #6c757d;
    }

    .no-data a {
      color: #007bff;
      text-decoration: none;
    }

    .no-data a:hover {
      text-decoration: underline;
    }

    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background-color: #0056b3;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #545b62;
    }

    .btn-info {
      background-color: #17a2b8;
      color: white;
    }

    .btn-info:hover {
      background-color: #117a8b;
    }

    .btn-danger {
      background-color: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background-color: #c82333;
    }

    .btn-sm {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }

    .icon {
      font-size: 1rem;
    }

    .loading-container {
      padding: 3rem;
      text-align: center;
      color: #6c757d;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-container {
      padding: 2rem;
    }

    .error-message {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;
    }

    .error-message h4 {
      color: #721c24;
      margin-bottom: 1rem;
    }

    .error-message p {
      color: #721c24;
      margin-bottom: 1.5rem;
    }
  `]
})
export class CustomerListComponent implements OnInit {
  public customers: Customer[] = [];
  public isLoading = false;
  public errorMessage = '';
  @Output() addCustomer = new EventEmitter<void>();
  @Output() editCustomer = new EventEmitter<Customer>();
  @Output() viewAccounts = new EventEmitter<Customer>();

  constructor(private customerService: CustomerService) {}

  ngOnInit(): void {
    this.getCustomers();
  }

  public getCustomers(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.customerService.getCustomers().subscribe({
      next: (response: Customer[]) => {
        this.customers = response;
        this.isLoading = false;
      },
      error: (error: Error) => {
        this.errorMessage = error.message;
        this.isLoading = false;
        console.error('Error loading customers:', error);
      }
    });
  }

  public retryLoad(): void {
    this.getCustomers();
  }

  public onAddCustomer(): void {
    this.addCustomer.emit();
  }

  public onEditCustomer(customer: Customer): void {
    this.editCustomer.emit(customer);
  }

  public onViewAccounts(customer: Customer): void {
    this.viewAccounts.emit(customer);
  }

  public onDeleteCustomer(customerId: number): void {
    if (confirm('Are you sure you want to delete this customer?')) {
      this.customerService.deleteCustomer(customerId).subscribe({
        next: () => {
          this.getCustomers(); // Refresh the list
        },
        error: (error: Error) => {
          alert('Error deleting customer: ' + error.message);
        }
      });
    }
  }
}
