import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BankAccount, AccountStatus } from '../../models/bank-account';
import { BankAccountService } from '../../services/bank-account.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-bank-account-list',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="account-list-container">
      <!-- Header Section -->
      <div class="header-section">
        <div class="header-content">
          <h1 class="page-title">
            <span class="title-icon">💳</span>
            {{ customerId ? 'Customer Accounts' : 'Bank Accounts' }}
          </h1>
          <p class="page-subtitle">
            {{ customerId ? 'Manage customer bank accounts' : 'Manage all bank accounts in the system' }}
          </p>
        </div>
        <button class="add-account-btn" (click)="onAddAccount()">
          <span class="btn-icon">+</span>
          <span class="btn-text">New Account</span>
        </button>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview" *ngIf="accounts.length > 0">
        <div class="stat-card">
          <div class="stat-value">{{ accounts.length }}</div>
          <div class="stat-label">Total Accounts</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getTotalBalance() | currency:'USD':'symbol':'1.0-0' }}</div>
          <div class="stat-label">Total Balance</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getActiveAccountsCount() }}</div>
          <div class="stat-label">Active Accounts</div>
        </div>
      </div>

      <!-- Accounts Grid -->
      <div class="accounts-grid" *ngIf="accounts.length > 0; else noAccounts">
        <div class="account-card" *ngFor="let account of accounts" [ngClass]="'card-' + getAccountTypeBadgeClass(account)">
          <!-- Card Header -->
          <div class="card-header">
            <div class="account-type">
              <span class="type-icon">{{ getAccountTypeIcon(account) }}</span>
              <span class="type-text">{{ getAccountTypeDisplay(account) }}</span>
            </div>
            <div class="account-status" [ngClass]="'status-' + account.status.toLowerCase()">
              {{ account.status }}
            </div>
          </div>

          <!-- Card Body -->
          <div class="card-body">
            <div class="account-id">
              <span class="id-label">Account ID</span>
              <span class="id-value">{{ account.id | slice:0:8 }}...</span>
            </div>

            <div class="balance-section">
              <div class="balance-amount">{{ account.balance | currency:'USD':'symbol':'1.2-2' }}</div>
              <div class="balance-label">Current Balance</div>
            </div>

            <div class="account-details" *ngIf="!customerId && account.customerDTO">
              <div class="customer-info">
                <span class="customer-icon">👤</span>
                <span class="customer-name">{{ account.customerDTO.name }}</span>
              </div>
            </div>

            <!-- Account Specific Info -->
            <div class="specific-info" *ngIf="account.type === 'SavingAccount'">
              <div class="info-item">
                <span class="info-icon">📈</span>
                <span class="info-text">Interest Rate: {{ getSavingAccountRate(account) }}%</span>
              </div>
            </div>

            <div class="specific-info" *ngIf="account.type === 'CurrentAccount'">
              <div class="info-item">
                <span class="info-icon">💰</span>
                <span class="info-text">Overdraft: {{ getCurrentAccountOverdraft(account) | currency:'USD':'symbol':'1.0-0' }}</span>
              </div>
            </div>
          </div>

          <!-- Card Actions -->
          <div class="card-actions">
            <button class="action-btn primary" (click)="onViewOperations(account)" title="View Operations">
              <span class="btn-icon">📊</span>
              <span class="btn-text">Operations</span>
            </button>
            <button class="action-btn secondary" (click)="onEditAccount(account)" title="Edit Account">
              <span class="btn-icon">✏️</span>
              <span class="btn-text">Edit</span>
            </button>
            <button class="action-btn danger" (click)="onDeleteAccount(account.id)" title="Delete Account">
              <span class="btn-icon">🗑️</span>
              <span class="btn-text">Delete</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <ng-template #noAccounts>
        <div class="empty-state">
          <div class="empty-icon">🏦</div>
          <h2 class="empty-title">No Accounts Found</h2>
          <p class="empty-description">
            {{ customerId ? 'This customer doesn\'t have any accounts yet.' : 'No bank accounts in the system.' }}
          </p>
          <button class="empty-action-btn" (click)="onAddAccount()">
            <span class="btn-icon">+</span>
            <span class="btn-text">Create First Account</span>
          </button>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    .account-list {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }

    .list-header h3 {
      margin: 0;
      color: #2c3e50;
    }

    .table-container {
      overflow-x: auto;
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid #dee2e6;
      vertical-align: middle;
      white-space: nowrap;
    }

    .data-table td.actions {
      white-space: normal;
      min-width: 200px;
    }

    .data-table th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }

    .data-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .balance {
      font-weight: 600;
      color: #28a745;
    }

    .badge {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 500;
      text-transform: uppercase;
    }

    .badge-checking {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .badge-savings {
      background-color: #e8f5e8;
      color: #388e3c;
    }

    .badge-business {
      background-color: #fff3e0;
      color: #f57c00;
    }

    .badge-saving {
      background-color: #e8f5e8;
      color: #388e3c;
    }

    .badge-current {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .badge-standard {
      background-color: #f8f9fa;
      color: #495057;
    }

    .status {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 500;
      text-transform: uppercase;
    }

    .status-active {
      background-color: #d4edda;
      color: #155724;
    }

    .status-inactive {
      background-color: #f8d7da;
      color: #721c24;
    }

    .status-suspended {
      background-color: #fff3cd;
      color: #856404;
    }

    .actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
      min-width: 200px;
    }

    .no-data {
      padding: 3rem;
      text-align: center;
      color: #6c757d;
    }

    .no-data a {
      color: #007bff;
      text-decoration: none;
    }

    .no-data a:hover {
      text-decoration: underline;
    }

    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background-color: #0056b3;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #545b62;
    }

    .btn-info {
      background-color: #17a2b8;
      color: white;
    }

    .btn-info:hover {
      background-color: #117a8b;
    }

    .btn-danger {
      background-color: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background-color: #c82333;
    }

    .btn-sm {
      padding: 0.375rem 0.75rem;
      font-size: 0.75rem;
      white-space: nowrap;
    }

    .icon {
      font-size: 1rem;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .data-table th,
      .data-table td {
        padding: 0.5rem;
        font-size: 0.875rem;
      }

      .actions {
        flex-direction: column;
        gap: 0.25rem;
        min-width: 120px;
      }

      .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
      }

      .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }
    }

    /* Account ID styling */
    .account-id {
      font-family: monospace;
      font-size: 0.8rem;
      color: #6c757d;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  `]
})
export class BankAccountListComponent implements OnInit {
  @Input() customerId: number | null = null;
  public accounts: BankAccount[] = [];
  @Output() addAccount = new EventEmitter<number | null>();
  @Output() editAccount = new EventEmitter<BankAccount>();
  @Output() viewOperations = new EventEmitter<BankAccount>();

  constructor(private bankAccountService: BankAccountService) {}

  ngOnInit(): void {
    this.getAccounts();
  }

  public getAccounts(): void {
    const request = this.customerId
      ? this.bankAccountService.getAccountsByCustomer(this.customerId)
      : this.bankAccountService.getAccounts();

    request.subscribe({
      next: (response: BankAccount[]) => {
        this.accounts = response;
      },
      error: (error: HttpErrorResponse) => {
        alert('Error loading accounts: ' + error.message);
      }
    });
  }

  public getAccountTypeDisplay(account: BankAccount): string {
    if (account.type === 'SavingAccount') return 'Saving';
    if (account.type === 'CurrentAccount') return 'Current';
    return 'Standard';
  }

  public getAccountTypeBadgeClass(account: BankAccount): string {
    if (account.type === 'SavingAccount') return 'saving';
    if (account.type === 'CurrentAccount') return 'current';
    return 'standard';
  }

  public onAddAccount(): void {
    this.addAccount.emit(this.customerId);
  }

  public onEditAccount(account: BankAccount): void {
    this.editAccount.emit(account);
  }

  public onViewOperations(account: BankAccount): void {
    this.viewOperations.emit(account);
  }

  public onDeleteAccount(accountId: string): void {
    if (confirm('Are you sure you want to delete this account?')) {
      this.bankAccountService.deleteAccount(accountId).subscribe({
        next: () => {
          this.getAccounts(); // Refresh the list
        },
        error: (error: HttpErrorResponse) => {
          alert('Error deleting account: ' + error.message);
        }
      });
    }
  }
}
