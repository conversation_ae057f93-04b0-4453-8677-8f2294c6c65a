import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BankAccount, AccountStatus } from '../../models/bank-account';
import { BankAccountService } from '../../services/bank-account.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-bank-account-list',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="account-list-container">
      <!-- Header Section -->
      <div class="header-section">
        <div class="header-content">
          <h1 class="page-title">
            <span class="title-icon">💳</span>
            {{ customerId ? 'Customer Accounts' : 'Bank Accounts' }}
          </h1>
          <p class="page-subtitle">
            {{ customerId ? 'Manage customer bank accounts' : 'Manage all bank accounts in the system' }}
          </p>
        </div>
        <button class="add-account-btn" (click)="onAddAccount()">
          <span class="btn-icon">+</span>
          <span class="btn-text">New Account</span>
        </button>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview" *ngIf="accounts.length > 0">
        <div class="stat-card">
          <div class="stat-value">{{ accounts.length }}</div>
          <div class="stat-label">Total Accounts</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getTotalBalance() | currency:'USD':'symbol':'1.0-0' }}</div>
          <div class="stat-label">Total Balance</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getActiveAccountsCount() }}</div>
          <div class="stat-label">Active Accounts</div>
        </div>
      </div>

      <!-- Debug Info -->
      <div style="background: white; padding: 1rem; margin: 1rem 0; border-radius: 10px;">
        <p><strong>Debug Info:</strong></p>
        <p>Accounts length: {{ accounts.length }}</p>
        <p>First account: {{ accounts[0] | json }}</p>
      </div>

      <!-- Simple Account Cards for Testing -->
      <div *ngIf="accounts.length > 0; else noAccounts">
        <div *ngFor="let account of accounts; trackBy: trackByAccountId"
             style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-left: 4px solid #667eea;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
            <h3 style="margin: 0; color: #2d3748;">Account {{ account.id.slice(0, 8) }}...</h3>
            <span style="background: #e2e8f0; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">{{ account.status }}</span>
          </div>
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
            <div>
              <div style="font-size: 0.8rem; color: #718096; text-transform: uppercase;">Balance</div>
              <div style="font-size: 1.5rem; font-weight: 700; color: #2d3748;">{{ account.balance | currency:'USD':'symbol':'1.2-2' }}</div>
            </div>
            <div>
              <div style="font-size: 0.8rem; color: #718096; text-transform: uppercase;">Type</div>
              <div style="font-size: 1rem; font-weight: 600; color: #2d3748;">{{ getAccountTypeDisplay(account) }}</div>
            </div>
            <div *ngIf="account.customerDTO">
              <div style="font-size: 0.8rem; color: #718096; text-transform: uppercase;">Customer</div>
              <div style="font-size: 1rem; font-weight: 600; color: #2d3748;">{{ account.customerDTO.name }}</div>
            </div>
          </div>
          <div style="display: flex; gap: 0.5rem;">
            <button (click)="onViewOperations(account)" style="background: #4299e1; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer;">Operations</button>
            <button (click)="onEditAccount(account)" style="background: #a0aec0; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer;">Edit</button>
            <button (click)="onDeleteAccount(account.id)" style="background: #f56565; color: white; border: none; padding: 0.5rem 1rem; border-radius: 8px; cursor: pointer;">Delete</button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <ng-template #noAccounts>
        <div class="empty-state">
          <div class="empty-icon">🏦</div>
          <h2 class="empty-title">No Accounts Found</h2>
          <p class="empty-description">
            {{ customerId ? 'This customer doesn\'t have any accounts yet.' : 'No bank accounts in the system.' }}
          </p>
          <button class="empty-action-btn" (click)="onAddAccount()">
            <span class="btn-icon">+</span>
            <span class="btn-text">Create First Account</span>
          </button>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    .account-list-container {
      padding: 2rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    /* Header Section */
    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .header-content {
      flex: 1;
    }

    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2d3748;
      margin: 0 0 0.5rem 0;
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .title-icon {
      font-size: 2.5rem;
    }

    .page-subtitle {
      font-size: 1.1rem;
      color: #718096;
      margin: 0;
    }

    .add-account-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 15px;
      padding: 1rem 2rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .add-account-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }

    .btn-icon {
      font-size: 1.2rem;
      font-weight: bold;
    }

    /* Stats Overview */
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 15px;
      padding: 1.5rem;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Accounts Grid */
    .accounts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 2rem;
    }

    .account-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .account-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .card-saving {
      border-color: #48bb78;
    }

    .card-current {
      border-color: #4299e1;
    }

    .card-standard {
      border-color: #a0aec0;
    }

    /* Card Header */
    .card-header {
      padding: 1.5rem;
      background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .account-type {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .type-icon {
      font-size: 1.5rem;
    }

    .type-text {
      font-size: 1.1rem;
      font-weight: 600;
      color: #2d3748;
    }

    .account-status {
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-active {
      background: #c6f6d5;
      color: #22543d;
    }

    .status-inactive {
      background: #fed7d7;
      color: #742a2a;
    }

    .status-suspended {
      background: #fefcbf;
      color: #744210;
    }

    /* Card Body */
    .card-body {
      padding: 1.5rem;
    }

    .account-id {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding: 0.75rem;
      background: #f7fafc;
      border-radius: 10px;
    }

    .id-label {
      font-size: 0.8rem;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .id-value {
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      color: #2d3748;
      font-weight: 600;
    }

    .balance-section {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .balance-amount {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 0.25rem;
    }

    .balance-label {
      font-size: 0.9rem;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .account-details {
      margin-bottom: 1rem;
    }

    .customer-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem;
      background: #f7fafc;
      border-radius: 10px;
    }

    .customer-icon {
      font-size: 1.2rem;
    }

    .customer-name {
      font-weight: 600;
      color: #2d3748;
    }

    .specific-info {
      margin-bottom: 1rem;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem;
      background: #f7fafc;
      border-radius: 10px;
    }

    .info-icon {
      font-size: 1.2rem;
    }

    .info-text {
      font-size: 0.9rem;
      color: #4a5568;
      font-weight: 500;
    }

    /* Card Actions */
    .card-actions {
      padding: 1.5rem;
      background: #f7fafc;
      display: flex;
      gap: 0.75rem;
      flex-wrap: wrap;
    }

    .action-btn {
      flex: 1;
      min-width: 100px;
      padding: 0.75rem 1rem;
      border: none;
      border-radius: 10px;
      font-size: 0.85rem;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .action-btn.primary {
      background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
      color: white;
    }

    .action-btn.primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(66, 153, 225, 0.4);
    }

    .action-btn.secondary {
      background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
      color: white;
    }

    .action-btn.secondary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(160, 174, 192, 0.4);
    }

    .action-btn.danger {
      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
      color: white;
    }

    .action-btn.danger:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
    }

    .empty-title {
      font-size: 2rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 1rem;
    }

    .empty-description {
      font-size: 1.1rem;
      color: #718096;
      margin-bottom: 2rem;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    .empty-action-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 15px;
      padding: 1rem 2rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .empty-action-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .account-list-container {
        padding: 1rem;
      }

      .header-section {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
      }

      .page-title {
        font-size: 2rem;
      }

      .accounts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .stats-overview {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .card-actions {
        flex-direction: column;
      }

      .action-btn {
        min-width: auto;
      }
    }
  `]
})
export class BankAccountListComponent implements OnInit {
  @Input() customerId: number | null = null;
  public accounts: BankAccount[] = [];
  @Output() addAccount = new EventEmitter<number | null>();
  @Output() editAccount = new EventEmitter<BankAccount>();
  @Output() viewOperations = new EventEmitter<BankAccount>();

  constructor(private bankAccountService: BankAccountService) {}

  ngOnInit(): void {
    this.getAccounts();
  }

  public getAccounts(): void {
    const request = this.customerId
      ? this.bankAccountService.getAccountsByCustomer(this.customerId)
      : this.bankAccountService.getAccounts();

    request.subscribe({
      next: (response: BankAccount[]) => {
        console.log('Accounts received from backend:', response);
        this.accounts = response;
        console.log('Accounts array after assignment:', this.accounts);
        console.log('Accounts length:', this.accounts.length);
      },
      error: (error: HttpErrorResponse) => {
        console.error('Error loading accounts:', error);
        alert('Error loading accounts: ' + error.message);
      }
    });
  }

  public getAccountTypeDisplay(account: BankAccount): string {
    if (account.type === 'SavingAccount') return 'Saving';
    if (account.type === 'CurrentAccount') return 'Current';
    return 'Standard';
  }

  public getAccountTypeBadgeClass(account: BankAccount): string {
    if (account.type === 'SavingAccount') return 'saving';
    if (account.type === 'CurrentAccount') return 'current';
    return 'standard';
  }

  public getAccountTypeIcon(account: BankAccount): string {
    if (account.type === 'SavingAccount') return '🏦';
    if (account.type === 'CurrentAccount') return '💳';
    return '🏛️';
  }

  public getTotalBalance(): number {
    return this.accounts.reduce((total, account) => total + account.balance, 0);
  }

  public getActiveAccountsCount(): number {
    return this.accounts.filter(account => account.status.toLowerCase() === 'active').length;
  }

  public getSavingAccountRate(account: BankAccount): number {
    // Since the backend doesn't return interest rate in the DTO, we'll show a default
    // In a real app, you'd need to fetch this from the backend or include it in the DTO
    return 5.5;
  }

  public getCurrentAccountOverdraft(account: BankAccount): number {
    // Since the backend doesn't return overdraft in the DTO, we'll show a default
    // In a real app, you'd need to fetch this from the backend or include it in the DTO
    return 1000;
  }

  public trackByAccountId(index: number, account: BankAccount): string {
    return account.id;
  }

  public onAddAccount(): void {
    this.addAccount.emit(this.customerId);
  }

  public onEditAccount(account: BankAccount): void {
    this.editAccount.emit(account);
  }

  public onViewOperations(account: BankAccount): void {
    this.viewOperations.emit(account);
  }

  public onDeleteAccount(accountId: string): void {
    if (confirm('Are you sure you want to delete this account?')) {
      this.bankAccountService.deleteAccount(accountId).subscribe({
        next: () => {
          this.getAccounts(); // Refresh the list
        },
        error: (error: HttpErrorResponse) => {
          alert('Error deleting account: ' + error.message);
        }
      });
    }
  }
}
