import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BankAccount, SavingBankAccount, CurrentBankAccount, AccountStatus } from '../../models/bank-account';
import { CreateAccountRequest, CreateCurrentAccountRequest, CreateSavingAccountRequest } from '../../models/account-requests';
import { Customer } from '../../models/customer';
import { BankAccountService } from '../../services/bank-account.service';
import { CustomerService } from '../../services/customer.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-bank-account-form',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="form-overlay" (click)="onCancel()">
      <div class="form-modal" (click)="$event.stopPropagation()">
        <div class="form-header">
          <h3>{{ isEditMode ? 'Edit Account' : 'Add New Account' }}</h3>
          <button class="close-btn" (click)="onCancel()">&times;</button>
        </div>

        <form (ngSubmit)="onSubmit()" #accountForm="ngForm" class="account-form">
          <!-- Account ID is auto-generated by backend, so we don't need this field for creation -->

          <div class="form-group">
            <label for="type">Account Type *</label>
            <select
              id="type"
              name="type"
              [(ngModel)]="accountType"
              required
              class="form-control"
              (change)="onAccountTypeChange()"
            >
              <option value="current">Current Account</option>
              <option value="saving">Saving Account</option>
            </select>
          </div>

          <!-- Currency might be handled by backend, removing for now -->

          <div class="form-group">
            <label for="customerId">Customer *</label>
            <select
              id="customerId"
              name="customerId"
              [(ngModel)]="selectedCustomerId"
              required
              class="form-control"
              [disabled]="preselectedCustomerId !== null"
            >
              <option value="">Select customer</option>
              <option *ngFor="let customer of customers" [value]="customer.id">
                {{ customer.name }} ({{ customer.email }})
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="balance">Initial Balance *</label>
            <input
              type="number"
              id="balance"
              name="balance"
              [(ngModel)]="initialBalance"
              required
              min="0"
              step="0.01"
              class="form-control"
              placeholder="0.00"
            >
          </div>

          <!-- Status is likely handled by backend, removing for now -->

          <!-- Saving Account specific field -->
          <div class="form-group" *ngIf="accountType === 'saving'">
            <label for="interestRate">Interest Rate (%) *</label>
            <input
              type="number"
              id="interestRate"
              name="interestRate"
              [(ngModel)]="interestRate"
              required
              min="0"
              step="0.01"
              class="form-control"
              placeholder="5.5"
            >
          </div>

          <!-- Current Account specific field -->
          <div class="form-group" *ngIf="accountType === 'current'">
            <label for="overdraftLimit">Overdraft Limit *</label>
            <input
              type="number"
              id="overdraftLimit"
              name="overdraftLimit"
              [(ngModel)]="overdraft"
              required
              min="0"
              step="0.01"
              class="form-control"
              placeholder="1000.00"
            >
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" (click)="onCancel()">
              Cancel
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="!accountForm.form.valid || isSubmitting"
            >
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update' : 'Create') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  `,
  styles: [`
    .form-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .form-modal {
      background: white;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid #dee2e6;
      background-color: #f8f9fa;
    }

    .form-header h3 {
      margin: 0;
      color: #2c3e50;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #6c757d;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .close-btn:hover {
      color: #495057;
    }

    .account-form {
      padding: 1.5rem;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #495057;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 1rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
      outline: none;
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control:disabled {
      background-color: #e9ecef;
      opacity: 1;
    }

    .form-control[readonly] {
      background-color: #e9ecef;
    }

    .form-control.ng-invalid.ng-touched {
      border-color: #dc3545;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid #dee2e6;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 1rem;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;
      min-width: 100px;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background-color: #0056b3;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #545b62;
    }
  `]
})
export class BankAccountFormComponent implements OnInit {
  @Input() accountToEdit: BankAccount | SavingBankAccount | CurrentBankAccount | null = null;
  @Input() preselectedCustomerId: number | null = null;
  @Output() formSubmit = new EventEmitter<BankAccount | SavingBankAccount | CurrentBankAccount>();
  @Output() formCancel = new EventEmitter<void>();

  // Form data
  public initialBalance: number = 0;
  public interestRate: number = 5.5; // default for saving accounts
  public overdraft: number = 1000; // default for current accounts

  public selectedCustomerId: number | null = null;
  public accountType: string = 'current'; // current, saving
  public customers: Customer[] = [];
  public isEditMode = false;
  public isSubmitting = false;

  constructor(
    private bankAccountService: BankAccountService,
    private customerService: CustomerService
  ) {}

  ngOnInit(): void {
    this.loadCustomers();

    if (this.accountToEdit) {
      this.isEditMode = true;
      // For edit mode, we would need to populate the form with existing data
      // This is more complex with the current backend structure
      this.selectedCustomerId = this.accountToEdit.customerId;
      this.initialBalance = this.accountToEdit.balance;

      // Determine account type from the type field
      if (this.accountToEdit.type === 'SavingAccount') {
        this.accountType = 'saving';
        if ('interestRate' in this.accountToEdit) {
          this.interestRate = (this.accountToEdit as SavingBankAccount).interestRate;
        }
      } else if (this.accountToEdit.type === 'CurrentAccount') {
        this.accountType = 'current';
        if ('overDraft' in this.accountToEdit) {
          this.overdraft = (this.accountToEdit as CurrentBankAccount).overDraft;
        }
      }
    } else if (this.preselectedCustomerId) {
      this.selectedCustomerId = this.preselectedCustomerId;
    }
  }

  private loadCustomers(): void {
    this.customerService.getCustomers().subscribe({
      next: (customers: Customer[]) => {
        this.customers = customers;
      },
      error: (error: HttpErrorResponse) => {
        alert('Error loading customers: ' + error.message);
      }
    });
  }

  public onAccountTypeChange(): void {
    // Reset to default values when account type changes
    if (this.accountType === 'saving') {
      this.interestRate = 5.5; // default interest rate
    } else if (this.accountType === 'current') {
      this.overdraft = 1000; // default overdraft
    }
  }

  public onSubmit(): void {
    if (this.isSubmitting) return;

    if (!this.selectedCustomerId) {
      alert('Please select a customer');
      return;
    }

    this.isSubmitting = true;

    if (this.isEditMode) {
      // Edit mode is more complex with the current backend structure
      alert('Edit functionality needs to be implemented based on backend update endpoints');
      this.isSubmitting = false;
      return;
    }

    // Create the appropriate request based on account type
    if (this.accountType === 'saving') {
      const request: CreateSavingAccountRequest = {
        initialBalance: this.initialBalance,
        interestRate: this.interestRate,
        customerId: this.selectedCustomerId
      };

      this.bankAccountService.addSavingAccount(request).subscribe({
        next: (newAccount: SavingBankAccount) => {
          this.formSubmit.emit(newAccount);
          this.isSubmitting = false;
        },
        error: (error: HttpErrorResponse) => {
          console.error('Error saving saving account:', error);
          alert(`Error saving account: ${error.message}`);
          this.isSubmitting = false;
        }
      });
    } else if (this.accountType === 'current') {
      const request: CreateCurrentAccountRequest = {
        initialBalance: this.initialBalance,
        overdraft: this.overdraft,
        customerId: this.selectedCustomerId
      };

      this.bankAccountService.addCurrentAccount(request).subscribe({
        next: (newAccount: CurrentBankAccount) => {
          this.formSubmit.emit(newAccount);
          this.isSubmitting = false;
        },
        error: (error: HttpErrorResponse) => {
          console.error('Error saving current account:', error);
          alert(`Error saving account: ${error.message}`);
          this.isSubmitting = false;
        }
      });
    }
  }

  public onCancel(): void {
    this.formCancel.emit();
  }
}
